# 基础聊天功能示例

## 概述

本文档提供了 DeepCreate 聊天功能的基础使用示例，包括发送消息、接收回复、错误处理等常见场景。

## 1. 基础聊天实现

### 1.1 发送简单文本消息

```typescript
import { fetchChatAPIProcess } from '@/api';
import { useChatStore } from '@/store/modules/chat';

// 发送基础文本消息
async function sendBasicMessage(message: string) {
  const chatStore = useChatStore();
  const activeGroupId = chatStore.active;

  if (!activeGroupId) {
    throw new Error('请先选择或创建对话组');
  }

  try {
    // 添加用户消息到界面
    chatStore.addGroupChat({
      text: message,
      inversion: true,
      error: false,
      loading: false,
      modelType: 1
    });

    // 添加AI回复占位符
    chatStore.addGroupChat({
      text: '',
      inversion: false,
      error: false,
      loading: true,
      modelType: 1
    });

    // 发送请求
    const response = await fetchChatAPIProcess({
      prompt: message,
      model: 'gpt-3.5-turbo',
      modelName: 'GPT-3.5',
      modelType: 1,
      options: {
        groupId: activeGroupId,
        usingNetwork: false
      }
    });

    return response;
  } catch (error) {
    // 更新最后一条消息为错误状态
    const lastIndex = chatStore.chatList.length - 1;
    chatStore.updateGroupChat(lastIndex, {
      error: true,
      loading: false,
      text: '发送失败，请重试'
    });
    
    throw error;
  }
}

// 使用示例
async function handleSendMessage() {
  const userInput = '你好，请介绍一下自己';
  
  try {
    await sendBasicMessage(userInput);
    console.log('消息发送成功');
  } catch (error) {
    console.error('发送消息失败:', error);
  }
}
```

### 1.2 带模型配置的消息发送

```typescript
interface ChatOptions {
  model?: string;
  temperature?: number;
  systemMessage?: string;
  maxTokens?: number;
}

async function sendMessageWithConfig(
  message: string, 
  options: ChatOptions = {}
) {
  const {
    model = 'gpt-3.5-turbo',
    temperature = 0.7,
    systemMessage = '你是一个友善的AI助手',
    maxTokens = 2000
  } = options;

  const chatStore = useChatStore();
  const activeGroupId = chatStore.active;

  try {
    const response = await fetchChatAPIProcess({
      prompt: message,
      model,
      modelName: getModelDisplayName(model),
      modelType: 1,
      systemMessage,
      options: {
        groupId: activeGroupId,
        temperature,
        usingNetwork: false
      }
    });

    return response;
  } catch (error) {
    console.error('发送配置消息失败:', error);
    throw error;
  }
}

// 获取模型显示名称
function getModelDisplayName(model: string): string {
  const modelNames: Record<string, string> = {
    'gpt-3.5-turbo': 'GPT-3.5',
    'gpt-4': 'GPT-4',
    'gpt-4-turbo': 'GPT-4 Turbo',
    'claude-3': 'Claude-3'
  };
  
  return modelNames[model] || model;
}

// 使用示例
async function sendCreativeMessage() {
  await sendMessageWithConfig(
    '请写一首关于春天的诗',
    {
      model: 'gpt-4',
      temperature: 0.9,
      systemMessage: '你是一位富有创意的诗人'
    }
  );
}
```

## 2. 聊天历史管理

### 2.1 加载聊天历史

```typescript
import { fetchQueryChatLogListAPI } from '@/api/chatLog';

async function loadChatHistory(groupId: number) {
  const chatStore = useChatStore();
  
  try {
    chatStore.setLoading(true);
    
    const chatList = await fetchQueryChatLogListAPI({ groupId });
    
    // 转换数据格式
    const formattedChats = chatList.map(chat => ({
      chatId: chat.chatId,
      text: chat.text,
      inversion: chat.inversion,
      error: chat.error,
      loading: false,
      modelType: chat.modelType,
      model: chat.model,
      modelName: chat.modelName,
      modelAvatar: chat.modelAvatar,
      dateTime: chat.dateTime,
      fileInfo: chat.fileInfo,
      ttsUrl: chat.ttsUrl,
      videoUrl: chat.videoUrl,
      audioUrl: chat.audioUrl
    }));
    
    chatStore.setChatList(formattedChats);
    
  } catch (error) {
    console.error('加载聊天历史失败:', error);
    chatStore.setChatList([]);
  } finally {
    chatStore.setLoading(false);
  }
}

// 监听对话组切换
watch(() => chatStore.active, async (newGroupId) => {
  if (newGroupId) {
    await loadChatHistory(newGroupId);
  }
});
```

### 2.2 聊天记录分页加载

```typescript
interface ChatPagination {
  page: number;
  size: number;
  hasMore: boolean;
  loading: boolean;
}

class ChatHistoryManager {
  private pagination: ChatPagination = {
    page: 1,
    size: 20,
    hasMore: true,
    loading: false
  };

  async loadMoreChats(groupId: number) {
    if (this.pagination.loading || !this.pagination.hasMore) {
      return;
    }

    try {
      this.pagination.loading = true;
      
      const response = await fetchQueryChatLogListAPI({
        groupId,
        page: this.pagination.page,
        size: this.pagination.size
      });

      const newChats = response.data || [];
      
      if (newChats.length < this.pagination.size) {
        this.pagination.hasMore = false;
      }

      // 将新数据添加到现有列表前面（历史记录）
      const chatStore = useChatStore();
      const existingChats = chatStore.chatList;
      chatStore.setChatList([...newChats, ...existingChats]);

      this.pagination.page++;
      
    } catch (error) {
      console.error('加载更多聊天记录失败:', error);
    } finally {
      this.pagination.loading = false;
    }
  }

  resetPagination() {
    this.pagination = {
      page: 1,
      size: 20,
      hasMore: true,
      loading: false
    };
  }
}

// 使用示例
const chatHistoryManager = new ChatHistoryManager();

// 滚动到顶部时加载更多
function handleScrollToTop() {
  const chatStore = useChatStore();
  const activeGroupId = chatStore.active;
  
  if (activeGroupId) {
    chatHistoryManager.loadMoreChats(activeGroupId);
  }
}
```

## 3. 消息状态管理

### 3.1 消息发送状态跟踪

```typescript
interface MessageState {
  id: string;
  status: 'sending' | 'sent' | 'failed' | 'received';
  timestamp: number;
  retryCount: number;
}

class MessageStateManager {
  private messageStates = new Map<string, MessageState>();

  // 创建消息状态
  createMessageState(messageId: string): MessageState {
    const state: MessageState = {
      id: messageId,
      status: 'sending',
      timestamp: Date.now(),
      retryCount: 0
    };
    
    this.messageStates.set(messageId, state);
    return state;
  }

  // 更新消息状态
  updateMessageState(messageId: string, status: MessageState['status']) {
    const state = this.messageStates.get(messageId);
    if (state) {
      state.status = status;
      state.timestamp = Date.now();
    }
  }

  // 重试消息
  async retryMessage(messageId: string, retryFn: () => Promise<void>) {
    const state = this.messageStates.get(messageId);
    if (!state || state.retryCount >= 3) {
      return false;
    }

    try {
      state.retryCount++;
      state.status = 'sending';
      
      await retryFn();
      
      state.status = 'sent';
      return true;
    } catch (error) {
      state.status = 'failed';
      return false;
    }
  }

  // 清理过期状态
  cleanupExpiredStates(maxAge = 5 * 60 * 1000) { // 5分钟
    const now = Date.now();
    for (const [id, state] of this.messageStates.entries()) {
      if (now - state.timestamp > maxAge) {
        this.messageStates.delete(id);
      }
    }
  }
}

// 使用示例
const messageStateManager = new MessageStateManager();

async function sendMessageWithState(message: string) {
  const messageId = `msg_${Date.now()}_${Math.random()}`;
  const state = messageStateManager.createMessageState(messageId);

  try {
    await sendBasicMessage(message);
    messageStateManager.updateMessageState(messageId, 'sent');
  } catch (error) {
    messageStateManager.updateMessageState(messageId, 'failed');
    
    // 提供重试选项
    const shouldRetry = await showRetryDialog();
    if (shouldRetry) {
      const success = await messageStateManager.retryMessage(
        messageId,
        () => sendBasicMessage(message)
      );
      
      if (!success) {
        showErrorMessage('消息发送失败，请稍后重试');
      }
    }
  }
}
```

### 3.2 消息队列管理

```typescript
interface QueuedMessage {
  id: string;
  content: string;
  options: any;
  timestamp: number;
  priority: number;
}

class MessageQueue {
  private queue: QueuedMessage[] = [];
  private processing = false;
  private maxRetries = 3;

  // 添加消息到队列
  enqueue(message: QueuedMessage) {
    this.queue.push(message);
    this.queue.sort((a, b) => b.priority - a.priority); // 按优先级排序
    
    if (!this.processing) {
      this.processQueue();
    }
  }

  // 处理队列
  private async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const message = this.queue.shift()!;
      
      try {
        await this.sendMessage(message);
      } catch (error) {
        console.error('队列消息发送失败:', error);
        
        // 重试逻辑
        if (message.priority > 0) {
          message.priority--;
          this.queue.unshift(message); // 重新加入队列头部
        }
      }
      
      // 避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.processing = false;
  }

  // 发送单个消息
  private async sendMessage(message: QueuedMessage) {
    return await fetchChatAPIProcess({
      prompt: message.content,
      ...message.options
    });
  }

  // 清空队列
  clear() {
    this.queue = [];
  }

  // 获取队列状态
  getStatus() {
    return {
      queueLength: this.queue.length,
      processing: this.processing
    };
  }
}

// 使用示例
const messageQueue = new MessageQueue();

function queueMessage(content: string, priority = 1) {
  const message: QueuedMessage = {
    id: `queue_${Date.now()}`,
    content,
    options: {
      model: 'gpt-3.5-turbo',
      modelName: 'GPT-3.5',
      modelType: 1,
      options: {
        groupId: useChatStore().active,
        usingNetwork: false
      }
    },
    timestamp: Date.now(),
    priority
  };

  messageQueue.enqueue(message);
}
```

## 4. 错误处理和用户反馈

### 4.1 统一错误处理

```typescript
interface ChatError {
  type: 'network' | 'auth' | 'validation' | 'server' | 'unknown';
  message: string;
  code?: string;
  retryable: boolean;
}

function parseChatError(error: any): ChatError {
  const status = error.response?.status;
  const message = error.response?.data?.message || error.message;
  const code = error.response?.data?.code;

  switch (status) {
    case 401:
      return {
        type: 'auth',
        message: '登录已过期，请重新登录',
        code,
        retryable: false
      };
    
    case 400:
      if (message.includes('余额不足')) {
        return {
          type: 'validation',
          message: '余额不足，请充值后继续使用',
          code,
          retryable: false
        };
      }
      return {
        type: 'validation',
        message: message || '请求参数错误',
        code,
        retryable: false
      };
    
    case 429:
      return {
        type: 'server',
        message: '请求过于频繁，请稍后再试',
        code,
        retryable: true
      };
    
    case 500:
      return {
        type: 'server',
        message: '服务器内部错误',
        code,
        retryable: true
      };
    
    default:
      if (error.code === 'NETWORK_ERROR') {
        return {
          type: 'network',
          message: '网络连接失败，请检查网络设置',
          code,
          retryable: true
        };
      }
      
      return {
        type: 'unknown',
        message: message || '未知错误',
        code,
        retryable: true
      };
  }
}

// 错误处理函数
async function handleChatError(error: any, context: string) {
  const chatError = parseChatError(error);
  
  // 记录错误日志
  console.error(`聊天错误 [${context}]:`, chatError);
  
  // 根据错误类型处理
  switch (chatError.type) {
    case 'auth':
      // 跳转到登录页
      await router.push('/login');
      break;
      
    case 'validation':
      if (chatError.message.includes('余额不足')) {
        // 显示充值对话框
        showRechargeDialog();
      } else {
        showErrorMessage(chatError.message);
      }
      break;
      
    case 'network':
      // 显示网络错误提示
      showNetworkErrorDialog(chatError.retryable);
      break;
      
    case 'server':
      if (chatError.retryable) {
        // 显示重试选项
        const shouldRetry = await showRetryDialog(chatError.message);
        return shouldRetry;
      } else {
        showErrorMessage(chatError.message);
      }
      break;
      
    default:
      showErrorMessage(chatError.message);
  }
  
  return false;
}
```

### 4.2 用户反馈组件

```typescript
// 消息提示函数
function showErrorMessage(message: string) {
  // 使用 UI 库的消息提示组件
  ElMessage.error(message);
}

function showSuccessMessage(message: string) {
  ElMessage.success(message);
}

function showInfoMessage(message: string) {
  ElMessage.info(message);
}

// 确认对话框
function showRetryDialog(message?: string): Promise<boolean> {
  return new Promise((resolve) => {
    ElMessageBox.confirm(
      message || '操作失败，是否重试？',
      '提示',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      resolve(true);
    }).catch(() => {
      resolve(false);
    });
  });
}

// 网络错误对话框
function showNetworkErrorDialog(retryable: boolean) {
  const message = retryable 
    ? '网络连接失败，请检查网络设置后重试'
    : '网络连接失败，请稍后再试';
    
  ElMessageBox.alert(message, '网络错误', {
    confirmButtonText: '确定',
    type: 'error'
  });
}

// 充值对话框
function showRechargeDialog() {
  ElMessageBox.confirm(
    '当前余额不足，是否前往充值？',
    '余额不足',
    {
      confirmButtonText: '去充值',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    router.push('/recharge');
  });
}
```
