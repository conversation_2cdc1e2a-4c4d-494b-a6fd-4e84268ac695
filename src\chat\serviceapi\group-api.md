# 对话组管理接口文档

## 概述

本文档详细描述了 DeepCreate 项目中对话组管理相关的 API 接口，包括创建、查询、更新、删除对话组等功能。

## 接口列表

### 1. 创建对话组

**接口地址**: `POST /group/create`

**功能描述**: 创建新的对话组，可以指定应用ID和模型配置。

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CreateGroupRequest {
  appId?: number;                    // 应用ID，可选
  modelConfig?: any;                 // 模型配置对象，可选
  params?: string;                   // 对话组参数序列化字符串，可选
}
```

**请求示例**:
```json
{
  "appId": 10,
  "modelConfig": {
    "model": "gpt-3.5-turbo",
    "temperature": 0.7,
    "maxTokens": 2000
  },
  "params": "{\"theme\":\"编程助手\",\"language\":\"中文\"}"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 123,
    "uuid": "uuid-string",
    "title": "新对话",
    "appId": 10,
    "userId": 456,
    "isSticky": false,
    "isDelete": false,
    "config": "{\"model\":\"gpt-3.5-turbo\"}",
    "fileUrl": null,
    "createdAt": "2024-01-20T10:30:00.000Z",
    "updatedAt": "2024-01-20T10:30:00.000Z"
  }
}
```

**前端实现示例**:
```typescript
async function createChatGroup(appId?: number, modelConfig?: any, params?: string) {
  try {
    const response = await post<any>({
      url: '/group/create',
      data: { appId, modelConfig, params }
    });
    return response.data;
  } catch (error) {
    console.error('创建对话组失败:', error);
    throw error;
  }
}
```

### 2. 查询对话组列表

**接口地址**: `GET /group/query`

**功能描述**: 查询当前用户的所有对话组列表。

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**: 无

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "uuid": "uuid-string-1",
      "title": "编程助手对话",
      "appId": 10,
      "userId": 456,
      "isSticky": true,
      "isDelete": false,
      "config": "{\"model\":\"gpt-3.5-turbo\",\"temperature\":0.7}",
      "fileUrl": null,
      "createdAt": "2024-01-20T10:30:00.000Z",
      "updatedAt": "2024-01-20T11:00:00.000Z"
    },
    {
      "id": 124,
      "uuid": "uuid-string-2",
      "title": "图像生成对话",
      "appId": null,
      "userId": 456,
      "isSticky": false,
      "isDelete": false,
      "config": "{\"model\":\"dall-e-3\"}",
      "fileUrl": null,
      "createdAt": "2024-01-20T09:15:00.000Z",
      "updatedAt": "2024-01-20T09:15:00.000Z"
    }
  ]
}
```

**响应字段说明**:
- `id`: 对话组唯一ID
- `uuid`: 对话组UUID
- `title`: 对话组标题
- `appId`: 关联的应用ID
- `userId`: 用户ID
- `isSticky`: 是否置顶
- `isDelete`: 是否已删除
- `config`: 配置信息JSON字符串
- `fileUrl`: 关联文件URL
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

**前端实现示例**:
```typescript
async function fetchGroupList() {
  try {
    const response = await get<any>({
      url: '/group/query'
    });
    return response.data;
  } catch (error) {
    console.error('获取对话组列表失败:', error);
    throw error;
  }
}
```

### 3. 获取对话组详情

**接口地址**: `GET /group/info/:id`

**功能描述**: 通过对话组ID获取详细信息。

**请求参数**:
- `id`: 对话组ID (路径参数)

**请求示例**:
```
GET /group/info/123
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "uuid": "uuid-string",
    "title": "编程助手对话",
    "appId": 10,
    "userId": 456,
    "isSticky": true,
    "isDelete": false,
    "config": "{\"model\":\"gpt-3.5-turbo\",\"temperature\":0.7}",
    "fileUrl": null,
    "createdAt": "2024-01-20T10:30:00.000Z",
    "updatedAt": "2024-01-20T11:00:00.000Z",
    "app": {
      "id": 10,
      "name": "编程助手",
      "description": "专业的编程辅助工具"
    }
  }
}
```

**前端实现示例**:
```typescript
async function fetchGroupInfo(groupId: number | string) {
  try {
    const response = await get<any>({
      url: `/group/info/${groupId}`
    });
    return response.data;
  } catch (error) {
    console.error('获取对话组详情失败:', error);
    throw error;
  }
}
```

### 4. 更新对话组

**接口地址**: `POST /group/update`

**功能描述**: 更新对话组的信息，如标题、置顶状态、配置等。

**请求参数**:
```typescript
interface UpdateGroupRequest {
  groupId: number;                   // 对话组ID
  title?: string;                    // 新标题
  isSticky?: boolean;                // 是否置顶
  config?: string;                   // 配置信息JSON字符串
  fileUrl?: string;                  // 文件URL
}
```

**请求示例**:
```json
{
  "groupId": 123,
  "title": "更新后的标题",
  "isSticky": true,
  "config": "{\"model\":\"gpt-4\",\"temperature\":0.8}"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "更新成功"
}
```

**前端实现示例**:
```typescript
async function updateGroup(params: UpdateGroupRequest) {
  try {
    const response = await post<any>({
      url: '/group/update',
      data: params
    });
    return response.message;
  } catch (error) {
    console.error('更新对话组失败:', error);
    throw error;
  }
}
```

### 5. 删除对话组

**接口地址**: `POST /group/del`

**功能描述**: 删除指定的对话组（软删除）。

**请求参数**:
```typescript
interface DeleteGroupRequest {
  groupId: number;                   // 对话组ID
}
```

**请求示例**:
```json
{
  "groupId": 123
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "删除成功"
}
```

**前端实现示例**:
```typescript
async function deleteGroup(groupId: number) {
  try {
    const response = await post<any>({
      url: '/group/del',
      data: { groupId }
    });
    return response.message;
  } catch (error) {
    console.error('删除对话组失败:', error);
    throw error;
  }
}
```

### 6. 删除所有非置顶对话组

**接口地址**: `POST /group/delAll`

**功能描述**: 删除当前用户所有非置顶的对话组。

**请求参数**: 无

**响应格式**:
```json
{
  "success": true,
  "message": "删除成功"
}
```

**前端实现示例**:
```typescript
async function deleteAllGroups() {
  try {
    const response = await post<any>({
      url: '/group/delAll',
      data: {}
    });
    return response.message;
  } catch (error) {
    console.error('删除所有对话组失败:', error);
    throw error;
  }
}
```

## 错误处理

### 常见错误情况

1. **对话组不存在** (400)
```json
{
  "success": false,
  "message": "非法操作、您在删除一个非法资源！",
  "code": "INVALID_RESOURCE"
}
```

2. **权限不足** (403)
```json
{
  "success": false,
  "message": "无权限操作此对话组",
  "code": "PERMISSION_DENIED"
}
```

3. **参数错误** (400)
```json
{
  "success": false,
  "message": "对话组ID不能为空",
  "code": "VALIDATION_ERROR"
}
```

### 错误处理示例

```typescript
async function handleGroupError(error: any, operation: string) {
  const status = error.response?.status;
  const message = error.response?.data?.message || error.message;

  switch (status) {
    case 400:
      if (message.includes('非法操作')) {
        showErrorMessage('对话组不存在或无权限访问');
      } else {
        showErrorMessage(message);
      }
      break;
    case 403:
      showErrorMessage('无权限执行此操作');
      break;
    default:
      showErrorMessage(`${operation}失败，请稍后重试`);
  }
}
```

## 使用建议

### 1. 对话组状态管理

```typescript
// 使用 Pinia store 管理对话组状态
const useChatGroupStore = defineStore('chatGroup', {
  state: () => ({
    groups: [] as any[],
    activeGroupId: null as number | null,
    loading: false
  }),

  getters: {
    activeGroup: (state) => 
      state.groups.find(g => g.id === state.activeGroupId),
    
    stickyGroups: (state) => 
      state.groups.filter(g => g.isSticky),
    
    normalGroups: (state) => 
      state.groups.filter(g => !g.isSticky)
  },

  actions: {
    async fetchGroups() {
      this.loading = true;
      try {
        this.groups = await fetchGroupList();
      } finally {
        this.loading = false;
      }
    },

    async createGroup(appId?: number, modelConfig?: any) {
      const newGroup = await createChatGroup(appId, modelConfig);
      this.groups.unshift(newGroup);
      this.activeGroupId = newGroup.id;
      return newGroup;
    },

    async updateGroup(params: UpdateGroupRequest) {
      await updateGroup(params);
      await this.fetchGroups(); // 重新获取列表
    },

    async deleteGroup(groupId: number) {
      await deleteGroup(groupId);
      this.groups = this.groups.filter(g => g.id !== groupId);
      
      // 如果删除的是当前活跃组，切换到第一个组
      if (this.activeGroupId === groupId) {
        this.activeGroupId = this.groups[0]?.id || null;
      }
    }
  }
});
```

### 2. 自动创建对话组

```typescript
// 确保始终有可用的对话组
async function ensureActiveGroup() {
  const groupStore = useChatGroupStore();
  
  if (!groupStore.groups.length) {
    await groupStore.createGroup();
  }
  
  if (!groupStore.activeGroupId) {
    groupStore.activeGroupId = groupStore.groups[0].id;
  }
}
```

### 3. 配置信息处理

```typescript
// 安全地解析和设置配置
function parseGroupConfig(configStr: string) {
  try {
    return JSON.parse(configStr || '{}');
  } catch {
    return {};
  }
}

function stringifyGroupConfig(config: any) {
  try {
    return JSON.stringify(config);
  } catch {
    return '{}';
  }
}
```
