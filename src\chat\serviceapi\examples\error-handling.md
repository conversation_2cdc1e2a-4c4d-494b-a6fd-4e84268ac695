# 错误处理示例

## 概述

本文档提供了 DeepCreate 聊天功能中各种错误处理的最佳实践，包括网络错误、认证错误、业务逻辑错误等的处理方法。

## 1. 错误类型定义

### 1.1 错误分类

```typescript
// 错误类型枚举
enum ErrorType {
  NETWORK = 'network',           // 网络错误
  AUTH = 'auth',                 // 认证错误
  VALIDATION = 'validation',     // 验证错误
  BUSINESS = 'business',         // 业务逻辑错误
  SERVER = 'server',             // 服务器错误
  UNKNOWN = 'unknown'            // 未知错误
}

// 错误严重级别
enum ErrorSeverity {
  LOW = 'low',                   // 低级别，用户可忽略
  MEDIUM = 'medium',             // 中级别，需要用户注意
  HIGH = 'high',                 // 高级别，阻止用户继续操作
  CRITICAL = 'critical'          // 严重错误，需要立即处理
}

// 统一错误接口
interface ChatError {
  type: ErrorType;
  severity: ErrorSeverity;
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  retryable: boolean;
  retryCount?: number;
  maxRetries?: number;
}
```

### 1.2 错误工厂类

```typescript
class ChatErrorFactory {
  static createNetworkError(originalError: any): ChatError {
    return {
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      code: 'NETWORK_ERROR',
      message: '网络连接失败，请检查网络设置',
      details: originalError,
      timestamp: Date.now(),
      retryable: true,
      maxRetries: 3
    };
  }

  static createAuthError(code: string, message: string): ChatError {
    return {
      type: ErrorType.AUTH,
      severity: ErrorSeverity.HIGH,
      code,
      message,
      timestamp: Date.now(),
      retryable: false
    };
  }

  static createValidationError(field: string, message: string): ChatError {
    return {
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.MEDIUM,
      code: 'VALIDATION_ERROR',
      message,
      details: { field },
      timestamp: Date.now(),
      retryable: false
    };
  }

  static createBusinessError(code: string, message: string, retryable = false): ChatError {
    return {
      type: ErrorType.BUSINESS,
      severity: ErrorSeverity.MEDIUM,
      code,
      message,
      timestamp: Date.now(),
      retryable
    };
  }

  static createServerError(status: number, message: string): ChatError {
    const severity = status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM;
    
    return {
      type: ErrorType.SERVER,
      severity,
      code: `SERVER_ERROR_${status}`,
      message,
      timestamp: Date.now(),
      retryable: status >= 500 || status === 429
    };
  }

  static fromHttpError(error: any): ChatError {
    const status = error.response?.status;
    const message = error.response?.data?.message || error.message;
    const code = error.response?.data?.code;

    switch (status) {
      case 401:
        return this.createAuthError('UNAUTHORIZED', '登录已过期，请重新登录');
      
      case 403:
        return this.createAuthError('FORBIDDEN', '权限不足，无法执行此操作');
      
      case 400:
        if (message.includes('余额不足')) {
          return this.createBusinessError('INSUFFICIENT_BALANCE', '余额不足，请充值后继续使用');
        }
        if (message.includes('提问信息不能为空')) {
          return this.createValidationError('prompt', '请输入消息内容');
        }
        return this.createValidationError('request', message || '请求参数错误');
      
      case 429:
        return this.createServerError(429, '请求过于频繁，请稍后再试');
      
      case 500:
      case 502:
      case 503:
      case 504:
        return this.createServerError(status, '服务器暂时不可用，请稍后重试');
      
      default:
        if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
          return this.createNetworkError(error);
        }
        
        return {
          type: ErrorType.UNKNOWN,
          severity: ErrorSeverity.MEDIUM,
          code: code || 'UNKNOWN_ERROR',
          message: message || '未知错误，请稍后重试',
          details: error,
          timestamp: Date.now(),
          retryable: true
        };
    }
  }
}
```

## 2. 错误处理器

### 2.1 全局错误处理器

```typescript
class GlobalErrorHandler {
  private errorQueue: ChatError[] = [];
  private isProcessing = false;
  private maxQueueSize = 50;

  // 处理错误
  async handleError(error: any, context?: string): Promise<boolean> {
    const chatError = ChatErrorFactory.fromHttpError(error);
    
    // 添加上下文信息
    if (context) {
      chatError.details = { ...chatError.details, context };
    }

    // 记录错误
    this.logError(chatError);
    
    // 添加到处理队列
    this.addToQueue(chatError);
    
    // 处理错误
    return await this.processError(chatError);
  }

  private logError(error: ChatError) {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type}] ${error.code}: ${error.message}`;
    
    switch (logLevel) {
      case 'error':
        console.error(logMessage, error.details);
        break;
      case 'warn':
        console.warn(logMessage, error.details);
        break;
      default:
        console.log(logMessage, error.details);
    }

    // 发送到错误监控服务
    this.sendToMonitoring(error);
  }

  private getLogLevel(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      default:
        return 'log';
    }
  }

  private addToQueue(error: ChatError) {
    this.errorQueue.push(error);
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }

    // 开始处理队列
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  private async processQueue() {
    this.isProcessing = true;

    while (this.errorQueue.length > 0) {
      const error = this.errorQueue.shift()!;
      await this.processError(error);
      
      // 避免处理过于频繁
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.isProcessing = false;
  }

  private async processError(error: ChatError): Promise<boolean> {
    switch (error.type) {
      case ErrorType.AUTH:
        return await this.handleAuthError(error);
      
      case ErrorType.NETWORK:
        return await this.handleNetworkError(error);
      
      case ErrorType.BUSINESS:
        return await this.handleBusinessError(error);
      
      case ErrorType.VALIDATION:
        return await this.handleValidationError(error);
      
      case ErrorType.SERVER:
        return await this.handleServerError(error);
      
      default:
        return await this.handleUnknownError(error);
    }
  }

  private async handleAuthError(error: ChatError): Promise<boolean> {
    switch (error.code) {
      case 'UNAUTHORIZED':
        // 清除本地认证信息
        const authStore = useAuthStore();
        authStore.logout();
        
        // 显示登录提示
        showAuthDialog('登录已过期，请重新登录');
        
        // 跳转到登录页
        router.push('/login');
        return false;
      
      case 'FORBIDDEN':
        showErrorMessage(error.message);
        return false;
      
      default:
        showErrorMessage(error.message);
        return false;
    }
  }

  private async handleNetworkError(error: ChatError): Promise<boolean> {
    // 检查网络连接
    if (!navigator.onLine) {
      showNetworkOfflineDialog();
      return false;
    }

    // 显示网络错误提示
    const shouldRetry = await showRetryDialog(
      '网络连接不稳定，是否重试？',
      error.retryable
    );

    return shouldRetry;
  }

  private async handleBusinessError(error: ChatError): Promise<boolean> {
    switch (error.code) {
      case 'INSUFFICIENT_BALANCE':
        const shouldRecharge = await showRechargeDialog();
        if (shouldRecharge) {
          router.push('/recharge');
        }
        return false;
      
      case 'MODEL_UNAVAILABLE':
        showModelUnavailableDialog(error.message);
        return false;
      
      default:
        showErrorMessage(error.message);
        return error.retryable;
    }
  }

  private async handleValidationError(error: ChatError): Promise<boolean> {
    showValidationError(error.message, error.details?.field);
    return false;
  }

  private async handleServerError(error: ChatError): Promise<boolean> {
    if (error.retryable) {
      const shouldRetry = await showRetryDialog(
        `服务器错误：${error.message}`,
        true
      );
      return shouldRetry;
    } else {
      showErrorMessage(error.message);
      return false;
    }
  }

  private async handleUnknownError(error: ChatError): Promise<boolean> {
    console.error('未知错误:', error);
    
    const shouldRetry = await showRetryDialog(
      '发生未知错误，是否重试？',
      error.retryable
    );
    
    return shouldRetry;
  }

  private sendToMonitoring(error: ChatError) {
    // 发送到错误监控服务（如 Sentry）
    if (error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH) {
      try {
        // Sentry.captureException(error);
        console.log('发送错误到监控服务:', error);
      } catch (e) {
        console.warn('发送错误监控失败:', e);
      }
    }
  }

  // 获取错误统计
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byType: {} as Record<string, number>,
      bySeverity: {} as Record<string, number>
    };

    for (const error of this.errorQueue) {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    }

    return stats;
  }
}

// 全局错误处理器实例
const globalErrorHandler = new GlobalErrorHandler();
```

### 2.2 重试机制

```typescript
interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: any) => boolean;
}

class RetryManager {
  private defaultOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryCondition: (error) => {
      const chatError = ChatErrorFactory.fromHttpError(error);
      return chatError.retryable;
    }
  };

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const config = { ...this.defaultOptions, ...options };
    let lastError: any;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // 检查是否应该重试
        if (attempt === config.maxRetries || !config.retryCondition!(error)) {
          throw error;
        }

        // 计算延迟时间
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffFactor, attempt),
          config.maxDelay
        );

        console.log(`操作失败，${delay}ms 后进行第 ${attempt + 1} 次重试`);
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  // 带用户确认的重试
  async executeWithUserRetry<T>(
    operation: () => Promise<T>,
    errorMessage: string,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const config = { ...this.defaultOptions, ...options };

    try {
      return await operation();
    } catch (error) {
      const chatError = ChatErrorFactory.fromHttpError(error);
      
      if (!chatError.retryable) {
        throw error;
      }

      // 询问用户是否重试
      const shouldRetry = await showRetryDialog(
        `${errorMessage}\n\n错误详情：${chatError.message}`,
        true
      );

      if (shouldRetry) {
        return await this.executeWithRetry(operation, {
          ...config,
          maxRetries: config.maxRetries - 1
        });
      } else {
        throw error;
      }
    }
  }
}

const retryManager = new RetryManager();

// 使用示例
async function sendMessageWithRetry(message: string) {
  return await retryManager.executeWithUserRetry(
    () => sendBasicMessage(message),
    '发送消息失败',
    {
      maxRetries: 2,
      baseDelay: 2000
    }
  );
}
```

## 3. 用户界面错误处理

### 3.1 错误提示组件

```vue
<template>
  <div class="error-handler">
    <!-- 错误提示 -->
    <Teleport to="body">
      <div v-if="currentError" class="error-overlay" @click="dismissError">
        <div class="error-dialog" @click.stop>
          <div class="error-header">
            <Icon :name="getErrorIcon(currentError.severity)" />
            <h3>{{ getErrorTitle(currentError.type) }}</h3>
            <button @click="dismissError" class="close-btn">×</button>
          </div>
          
          <div class="error-content">
            <p>{{ currentError.message }}</p>
            
            <div v-if="currentError.details" class="error-details">
              <details>
                <summary>详细信息</summary>
                <pre>{{ JSON.stringify(currentError.details, null, 2) }}</pre>
              </details>
            </div>
          </div>
          
          <div class="error-actions">
            <button 
              v-if="currentError.retryable"
              @click="retryOperation"
              class="retry-btn"
              :disabled="retrying"
            >
              {{ retrying ? '重试中...' : '重试' }}
            </button>
            
            <button 
              v-if="currentError.type === 'business' && currentError.code === 'INSUFFICIENT_BALANCE'"
              @click="goToRecharge"
              class="recharge-btn"
            >
              去充值
            </button>
            
            <button @click="dismissError" class="dismiss-btn">
              {{ currentError.retryable ? '取消' : '确定' }}
            </button>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- 网络状态指示器 -->
    <div v-if="!isOnline" class="network-status offline">
      <Icon name="wifi-off" />
      <span>网络连接已断开</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import Icon from '@/components/common/Icon.vue';

const currentError = ref<ChatError | null>(null);
const retrying = ref(false);
const isOnline = ref(navigator.onLine);
const retryCallback = ref<(() => Promise<void>) | null>(null);

// 显示错误
function showError(error: ChatError, onRetry?: () => Promise<void>) {
  currentError.value = error;
  retryCallback.value = onRetry || null;
}

// 关闭错误提示
function dismissError() {
  currentError.value = null;
  retryCallback.value = null;
  retrying.value = false;
}

// 重试操作
async function retryOperation() {
  if (!retryCallback.value) return;

  retrying.value = true;
  
  try {
    await retryCallback.value();
    dismissError();
  } catch (error) {
    // 重试失败，更新错误信息
    const newError = ChatErrorFactory.fromHttpError(error);
    currentError.value = newError;
  } finally {
    retrying.value = false;
  }
}

// 跳转到充值页面
function goToRecharge() {
  router.push('/recharge');
  dismissError();
}

// 获取错误图标
function getErrorIcon(severity: ErrorSeverity): string {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 'error-circle';
    case ErrorSeverity.HIGH:
      return 'alert-triangle';
    case ErrorSeverity.MEDIUM:
      return 'alert-circle';
    default:
      return 'info-circle';
  }
}

// 获取错误标题
function getErrorTitle(type: ErrorType): string {
  switch (type) {
    case ErrorType.NETWORK:
      return '网络错误';
    case ErrorType.AUTH:
      return '认证错误';
    case ErrorType.VALIDATION:
      return '输入错误';
    case ErrorType.BUSINESS:
      return '操作失败';
    case ErrorType.SERVER:
      return '服务器错误';
    default:
      return '系统错误';
  }
}

// 监听网络状态
function handleOnline() {
  isOnline.value = true;
}

function handleOffline() {
  isOnline.value = false;
}

onMounted(() => {
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
});

onUnmounted(() => {
  window.removeEventListener('online', handleOnline);
  window.removeEventListener('offline', handleOffline);
});

// 暴露方法给父组件
defineExpose({
  showError,
  dismissError
});
</script>

<style scoped>
.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.error-dialog {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.error-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.error-header h3 {
  flex: 1;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-content {
  padding: 20px;
}

.error-content p {
  margin: 0 0 16px;
  line-height: 1.6;
  color: #333;
}

.error-details {
  margin-top: 16px;
}

.error-details summary {
  cursor: pointer;
  color: #666;
  font-size: 14px;
}

.error-details pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  overflow: auto;
  margin-top: 8px;
}

.error-actions {
  display: flex;
  gap: 12px;
  padding: 0 20px 20px;
  justify-content: flex-end;
}

.error-actions button {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.retry-btn {
  background: #007bff;
  color: white;
}

.retry-btn:hover:not(:disabled) {
  background: #0056b3;
}

.retry-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.recharge-btn {
  background: #28a745;
  color: white;
}

.recharge-btn:hover {
  background: #1e7e34;
}

.dismiss-btn {
  background: #6c757d;
  color: white;
}

.dismiss-btn:hover {
  background: #545b62;
}

.network-status {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  z-index: 1000;
  transition: all 0.3s;
}

.network-status.offline {
  background: #ff4757;
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
</style>
```

### 3.2 错误处理 Hook

```typescript
export function useErrorHandler() {
  const errorHandlerRef = ref<any>(null);

  // 处理聊天错误
  async function handleChatError(
    error: any, 
    context: string,
    onRetry?: () => Promise<void>
  ): Promise<boolean> {
    const chatError = ChatErrorFactory.fromHttpError(error);
    
    // 记录错误
    console.error(`聊天错误 [${context}]:`, chatError);
    
    // 显示错误提示
    if (errorHandlerRef.value) {
      errorHandlerRef.value.showError(chatError, onRetry);
    }
    
    // 返回是否可以重试
    return chatError.retryable;
  }

  // 处理流式聊天错误
  function handleStreamError(
    error: any,
    messageIndex: number,
    onRetry?: () => Promise<void>
  ) {
    const chatStore = useChatStore();
    
    // 更新消息状态
    chatStore.updateGroupChat(messageIndex, {
      error: true,
      loading: false,
      text: '发送失败，请重试'
    });
    
    // 处理错误
    handleChatError(error, 'stream-chat', onRetry);
  }

  // 处理网络错误
  function handleNetworkError(error: any, onRetry?: () => Promise<void>) {
    if (!navigator.onLine) {
      showMessage('网络连接已断开，请检查网络设置', 'error');
      return;
    }
    
    handleChatError(error, 'network', onRetry);
  }

  return {
    errorHandlerRef,
    handleChatError,
    handleStreamError,
    handleNetworkError
  };
}
```
