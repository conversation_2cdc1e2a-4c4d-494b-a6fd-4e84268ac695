# TypeScript 类型定义

## 概述

本文档定义了 DeepCreate 聊天功能中使用的所有 TypeScript 类型接口，确保前后端数据结构的一致性。

## 基础类型定义

### API 响应基础类型

```typescript
// 通用 API 响应格式
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  code?: string;
}

// 分页响应格式
interface PaginatedResponse<T = any> {
  rows: T[];
  count: number;
  page: number;
  size: number;
}

// 错误响应格式
interface ApiError {
  success: false;
  message: string;
  code: string;
  data: null;
}
```

## 聊天相关类型

### 聊天消息类型

```typescript
// 聊天消息接口
interface ChatMessage {
  chatId?: number;                   // 聊天记录ID
  text: string;                      // 消息内容
  modelType?: number;                // 模型类型：1-文本，2-图像，3-音频
  status?: number;                   // 消息状态：1-正常，2-处理中，3-失败
  fileInfo?: string;                 // 文件信息JSON字符串
  model?: string;                    // 使用的模型名称
  modelName?: string;                // 模型显示名称
  inversion?: boolean;               // 是否为用户消息
  ttsUrl?: string;                   // 语音文件URL
  videoUrl?: string;                 // 视频文件URL
  audioUrl?: string;                 // 音频文件URL
  action?: string;                   // 操作类型
  drawId?: string;                   // 绘图ID
  customId?: string;                 // 自定义ID
  usage?: TokenUsage;                // Token使用情况
  error?: boolean;                   // 是否有错误
  loading?: boolean;                 // 是否加载中
  usingPlugin?: string;              // 使用的插件
  modelAvatar?: string;              // 模型头像
  taskData?: any;                    // 任务数据
  conversationOptions?: ConversationRequest | null;
  pluginParam?: string;              // 插件参数
  promptReference?: string;          // 提示词引用
  dateTime?: string;                 // 消息时间
}

// Token 使用情况
interface TokenUsage {
  prompt_tokens: number;             // 提示词Token数
  completion_tokens: number;         // 完成Token数
  total_tokens: number;              // 总Token数
}

// 对话请求选项
interface ConversationRequest {
  conversationId?: string;
  parentMessageId?: string;
  temperature?: number;
  top_p?: number;
  groupId?: number;
  usingNetwork?: boolean;
}
```

### 聊天请求类型

```typescript
// 聊天处理请求
interface ChatProcessRequest {
  prompt: string;                    // 用户输入内容
  model?: string;                    // 模型名称
  modelName?: string;                // 模型显示名称
  modelType?: number;                // 模型类型
  modelAvatar?: string;              // 模型头像
  appId?: number;                    // 应用ID
  usingPluginId?: number;            // 插件ID
  fileInfo?: string;                 // 文件信息
  extraParam?: {                     // 额外参数
    size?: string;
    [key: string]: any;
  };
  options?: {
    groupId: number;                 // 对话组ID
    parentMessageId?: string;        // 父消息ID
    temperature?: number;            // 温度参数
    top_p?: number;                  // Top-p参数
    usingNetwork?: boolean;          // 是否使用网络
  };
  systemMessage?: string;            // 系统消息
  url?: string;                      // 附带链接
  action?: string;                   // 操作类型
  drawId?: string;                   // 绘图ID
  customId?: string;                 // 自定义ID
}

// 同步聊天请求
interface ChatSyncRequest {
  prompt: string;
  options?: {
    conversationId?: string;
    parentMessageId?: string;
    temperature: number;
  };
}

// TTS 请求
interface TTSRequest {
  chatId: number;                    // 聊天记录ID
  prompt: string;                    // 要转换的文字
}
```

### 聊天响应类型

```typescript
// 聊天流式响应数据
interface ChatStreamData {
  text?: string;                     // 文本片段
  userBalance?: number;              // 用户余额
  chatId?: number;                   // 聊天记录ID
  fileInfo?: string;                 // 文件信息
  error?: string;                    // 错误信息
  usage?: TokenUsage;                // Token使用情况
}

// 聊天同步响应
interface ChatSyncResponse {
  text: string;                      // 完整回复内容
  chatId: number;                    // 聊天记录ID
  userBalance: number;               // 用户余额
  usage: TokenUsage;                 // Token使用情况
}

// TTS 响应
interface TTSResponse {
  audioUrl: string;                  // 音频文件URL
  duration: number;                  // 音频时长（秒）
}
```

## 对话组相关类型

### 对话组类型

```typescript
// 对话组接口
interface ChatGroup {
  id: number;                        // 对话组ID
  uuid: string;                      // 对话组UUID
  title: string;                     // 对话组标题
  appId?: number;                    // 关联应用ID
  userId: number;                    // 用户ID
  isSticky: boolean;                 // 是否置顶
  isDelete: boolean;                 // 是否已删除
  config: string;                    // 配置信息JSON字符串
  fileUrl?: string;                  // 关联文件URL
  createdAt: string;                 // 创建时间
  updatedAt: string;                 // 更新时间
  app?: AppInfo;                     // 关联应用信息
}

// 应用信息
interface AppInfo {
  id: number;
  name: string;
  description: string;
  avatar?: string;
  status: number;
}

// 对话组历史记录
interface ChatHistory {
  title: string;
  isEdit: boolean;
  uuid: number;
  isSticky: boolean;
  config: string;
}
```

### 对话组请求类型

```typescript
// 创建对话组请求
interface CreateGroupRequest {
  appId?: number;                    // 应用ID
  modelConfig?: any;                 // 模型配置
  params?: string;                   // 参数字符串
}

// 更新对话组请求
interface UpdateGroupRequest {
  groupId: number;                   // 对话组ID
  title?: string;                    // 新标题
  isSticky?: boolean;                // 是否置顶
  config?: string;                   // 配置信息
  fileUrl?: string;                  // 文件URL
}

// 删除对话组请求
interface DeleteGroupRequest {
  groupId: number;                   // 对话组ID
}
```

## 聊天记录相关类型

### 聊天记录类型

```typescript
// 聊天记录查询请求
interface ChatListRequest {
  groupId?: number;                  // 对话组ID
}

// 删除聊天记录请求
interface DeleteChatRequest {
  id: number;                        // 聊天记录ID
}

// 删除对话组聊天记录请求
interface DeleteGroupChatsRequest {
  groupId: number;                   // 对话组ID
}

// 删除指定ID后聊天记录请求
interface DeleteChatsAfterIdRequest {
  id: number;                        // 聊天记录ID
}

// 按应用ID查询请求
interface QueryByAppIdRequest {
  appId: number;                     // 应用ID
  page?: number;                     // 页码
  size?: number;                     // 每页数量
}
```

## 模型相关类型

### 模型配置类型

```typescript
// 模型配置
interface ModelConfig {
  model: string;                     // 模型名称
  modelName: string;                 // 模型显示名称
  modelType: number;                 // 模型类型
  temperature?: number;              // 温度参数
  top_p?: number;                    // Top-p参数
  maxTokens?: number;                // 最大Token数
  systemMessage?: string;            // 系统消息
}

// 模型信息
interface ModelInfo {
  id: number;
  name: string;
  displayName: string;
  type: number;
  avatar?: string;
  description?: string;
  maxTokens: number;
  supportStream: boolean;
  supportImage: boolean;
  supportAudio: boolean;
  status: number;
}
```

## 文件相关类型

### 文件信息类型

```typescript
// 文件信息
interface FileInfo {
  url: string;                       // 文件URL
  name: string;                      // 文件名
  size: number;                      // 文件大小
  type: string;                      // 文件类型
  uploadTime: string;                // 上传时间
}

// 图像信息
interface ImageInfo {
  url: string;                       // 图片URL
  width: number;                     // 图片宽度
  height: number;                    // 图片高度
  format: string;                    // 图片格式
  size: number;                      // 文件大小
}

// 音频信息
interface AudioInfo {
  url: string;                       // 音频URL
  duration: number;                  // 音频时长
  format: string;                    // 音频格式
  size: number;                      // 文件大小
}
```

## 状态管理类型

### Store 状态类型

```typescript
// 聊天 Store 状态
interface ChatState {
  active: number | null;             // 当前活跃对话组ID
  groupList: ChatGroup[];            // 对话组列表
  chatList: ChatMessage[];           // 聊天记录列表
  loading: boolean;                  // 加载状态
  streamIn: boolean;                 // 是否正在流式输入
  baseConfig: ModelConfig;           // 基础模型配置
}

// 全局 Store 状态
interface GlobalState {
  isChatIn: boolean;                 // 是否正在聊天
  theme: 'light' | 'dark';           // 主题模式
  language: string;                  // 语言设置
  userInfo: UserInfo;                // 用户信息
}

// 用户信息
interface UserInfo {
  id: number;
  username: string;
  email: string;
  avatar?: string;
  balance: number;
  role: string;
  status: number;
}
```

## 工具类型

### 通用工具类型

```typescript
// 可选字段类型
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// 必需字段类型
type Required<T, K extends keyof T> = T & Required<Pick<T, K>>;

// 深度可选类型
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 排除null和undefined
type NonNullable<T> = T extends null | undefined ? never : T;

// 函数类型
type AsyncFunction<T = any> = (...args: any[]) => Promise<T>;
type EventHandler<T = any> = (event: T) => void;
type ProgressHandler = (progress: number) => void;
```

## 导出类型

```typescript
// 导出所有类型
export type {
  // API 基础类型
  ApiResponse,
  PaginatedResponse,
  ApiError,
  
  // 聊天相关类型
  ChatMessage,
  TokenUsage,
  ConversationRequest,
  ChatProcessRequest,
  ChatSyncRequest,
  TTSRequest,
  ChatStreamData,
  ChatSyncResponse,
  TTSResponse,
  
  // 对话组相关类型
  ChatGroup,
  AppInfo,
  ChatHistory,
  CreateGroupRequest,
  UpdateGroupRequest,
  DeleteGroupRequest,
  
  // 聊天记录相关类型
  ChatListRequest,
  DeleteChatRequest,
  DeleteGroupChatsRequest,
  DeleteChatsAfterIdRequest,
  QueryByAppIdRequest,
  
  // 模型相关类型
  ModelConfig,
  ModelInfo,
  
  // 文件相关类型
  FileInfo,
  ImageInfo,
  AudioInfo,
  
  // 状态管理类型
  ChatState,
  GlobalState,
  UserInfo,
  
  // 工具类型
  Optional,
  Required,
  DeepPartial,
  NonNullable,
  AsyncFunction,
  EventHandler,
  ProgressHandler
};
```
