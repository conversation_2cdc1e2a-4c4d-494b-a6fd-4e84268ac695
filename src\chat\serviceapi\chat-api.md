# 聊天核心接口文档

## 概述

本文档详细描述了 DeepCreate 项目中聊天功能的核心 API 接口，包括流式聊天、同步聊天、特殊模型聊天等功能。

## 接口列表

### 1. 流式聊天对话

**接口地址**: `POST /chatgpt/chat-process`

**功能描述**: 支持实时流式响应的聊天接口，适用于需要逐字显示 AI 回复的场景。

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface ChatProcessRequest {
  prompt: string;                    // 用户输入的消息内容
  model?: string;                    // 使用的模型名称，如 'gpt-3.5-turbo'
  modelName?: string;                // 模型显示名称
  modelType?: number;                // 模型类型：1-文本，2-图像，3-音频
  modelAvatar?: string;              // 模型头像URL
  appId?: number;                    // 应用ID，使用特定应用时传入
  usingPluginId?: number;            // 插件ID，使用插件时传入
  fileInfo?: string;                 // 文件信息，JSON字符串格式
  extraParam?: {                     // 额外参数
    size?: string;                   // 图像生成尺寸等
  };
  options?: {
    groupId: number;                 // 对话组ID
    parentMessageId?: string;        // 父消息ID，用于上下文关联
    temperature?: number;            // 温度参数，控制回复随机性 (0-1)
    top_p?: number;                  // Top-p 参数 (0-1)
    usingNetwork?: boolean;          // 是否使用网络搜索
  };
  systemMessage?: string;            // 系统提示词
  url?: string;                      // 附带的链接地址
}
```

**请求示例**:
```json
{
  "prompt": "你好，请介绍一下自己",
  "model": "gpt-3.5-turbo",
  "modelName": "GPT-3.5",
  "modelType": 1,
  "options": {
    "groupId": 123,
    "temperature": 0.7,
    "top_p": 1,
    "usingNetwork": false
  },
  "systemMessage": "你是一个友善的AI助手"
}
```

**响应格式**:
流式响应，每行返回一个 JSON 对象：
```
{"text": "你好"}
{"text": "！我是"}
{"text": "DeepCreate"}
{"text": "的AI助手"}
{"userBalance": 95}
{"chatId": 12345}
```

**响应字段说明**:
- `text`: 当前返回的文本片段
- `userBalance`: 用户余额更新
- `chatId`: 聊天记录ID
- `fileInfo`: 文件信息（如生成的图片等）
- `error`: 错误信息（如果有）

**前端实现示例**:
```typescript
async function sendStreamMessage(params: ChatProcessRequest, onProgress: (data: any) => void) {
  try {
    const response = await fetch('/chatgpt/chat-process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      const text = decoder.decode(value);
      buffer += text;

      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            onProgress(data);
          } catch (e) {
            console.error('解析JSON失败:', e, line);
          }
        }
      }
    }
  } catch (error) {
    console.error('流式聊天失败:', error);
    throw error;
  }
}
```

### 2. 同步聊天对话

**接口地址**: `POST /chatgpt/chat-sync`

**功能描述**: 同步返回完整回复的聊天接口，适用于不需要流式显示的场景。

**请求参数**: 与流式接口相同

**响应格式**:
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "text": "完整的回复内容",
    "chatId": 12345,
    "userBalance": 95,
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 20,
      "total_tokens": 30
    }
  }
}
```

**前端实现示例**:
```typescript
async function sendSyncMessage(params: ChatProcessRequest) {
  try {
    const response = await post<any>({
      url: '/chatgpt/chat-sync',
      data: params
    });
    return response.data;
  } catch (error) {
    console.error('同步聊天失败:', error);
    throw error;
  }
}
```

### 3. MJ 描述词翻译

**接口地址**: `POST /chatgpt/mj-fy`

**功能描述**: 将中文描述词翻译为适合 Midjourney 的英文提示词。

**请求参数**: 与基础聊天接口相同，但会自动使用 'PromptOptimization' 特殊模型

**请求示例**:
```json
{
  "prompt": "一只可爱的小猫在花园里玩耍",
  "options": {
    "groupId": 123
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "text": "A cute kitten playing in a garden, adorable, playful, colorful flowers, natural lighting, high quality, detailed"
  }
}
```

### 4. 思维导图生成

**接口地址**: `POST /chatgpt/chat-mind`

**功能描述**: 生成思维导图格式的内容，支持流式响应。

**请求参数**: 与基础聊天接口相同，但会自动使用 'MindMap' 特殊模型

**请求示例**:
```json
{
  "prompt": "请为"人工智能"这个主题生成思维导图",
  "options": {
    "groupId": 123
  }
}
```

**响应格式**: 流式响应，返回 Markdown 格式的思维导图结构

### 5. 文字转语音

**接口地址**: `POST /chatgpt/tts-process`

**功能描述**: 将文字转换为语音文件。

**请求参数**:
```typescript
interface TTSRequest {
  chatId: number;                    // 聊天记录ID
  prompt: string;                    // 要转换的文字内容
}
```

**请求示例**:
```json
{
  "chatId": 12345,
  "prompt": "你好，欢迎使用DeepCreate"
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "audioUrl": "https://example.com/audio/12345.mp3",
    "duration": 3.5
  }
}
```

## 错误处理

### 常见错误情况

1. **认证失败** (401)
```json
{
  "success": false,
  "message": "未授权访问",
  "code": "UNAUTHORIZED"
}
```

2. **参数错误** (400)
```json
{
  "success": false,
  "message": "提问信息不能为空！",
  "code": "VALIDATION_ERROR"
}
```

3. **余额不足** (400)
```json
{
  "success": false,
  "message": "余额不足，请充值后继续使用",
  "code": "INSUFFICIENT_BALANCE"
}
```

4. **模型不可用** (400)
```json
{
  "success": false,
  "message": "当前模型暂时不可用",
  "code": "MODEL_UNAVAILABLE"
}
```

### 错误处理建议

```typescript
async function handleChatError(error: any) {
  const status = error.response?.status;
  const message = error.response?.data?.message || error.message;

  switch (status) {
    case 401:
      // 跳转到登录页
      router.push('/login');
      break;
    case 400:
      if (message.includes('余额不足')) {
        // 显示充值提示
        showRechargeDialog();
      } else {
        // 显示错误信息
        showErrorMessage(message);
      }
      break;
    case 429:
      // 请求频率限制
      showErrorMessage('请求过于频繁，请稍后再试');
      break;
    default:
      showErrorMessage('网络错误，请稍后重试');
  }
}
```

## 最佳实践

1. **流式响应处理**: 使用 AbortController 支持取消请求
2. **错误重试**: 实现指数退避重试机制
3. **内存管理**: 及时清理流式响应的监听器
4. **用户体验**: 显示加载状态和进度指示器
5. **数据验证**: 在发送请求前验证参数完整性
