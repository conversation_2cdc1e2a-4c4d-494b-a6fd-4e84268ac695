# 聊天记录管理接口文档

## 概述

本文档详细描述了 DeepCreate 项目中聊天记录管理相关的 API 接口，包括查询、删除、导出等功能。

## 接口列表

### 1. 查询聊天记录列表

**接口地址**: `GET /chatlog/chatList`

**功能描述**: 查询指定对话组的聊天记录列表。

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface ChatListRequest {
  groupId?: number;                  // 对话组ID，可选
}
```

**请求示例**:
```
GET /chatlog/chatList?groupId=123
```

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "chatId": 12345,
      "dateTime": "2024-01-20 10:30:00",
      "text": "用户输入的消息",
      "modelType": 1,
      "status": 1,
      "inversion": true,
      "error": false,
      "model": "gpt-3.5-turbo",
      "modelName": "GPT-3.5",
      "modelAvatar": "https://example.com/avatar.png",
      "fileInfo": null,
      "ttsUrl": null,
      "videoUrl": null,
      "audioUrl": null,
      "pluginParam": null,
      "taskData": null,
      "promptReference": null
    },
    {
      "chatId": 12346,
      "dateTime": "2024-01-20 10:30:05",
      "text": "AI的回复内容",
      "modelType": 1,
      "status": 1,
      "inversion": false,
      "error": false,
      "model": "gpt-3.5-turbo",
      "modelName": "GPT-3.5",
      "modelAvatar": "https://example.com/avatar.png",
      "fileInfo": null,
      "ttsUrl": "https://example.com/audio/12346.mp3",
      "videoUrl": null,
      "audioUrl": null,
      "pluginParam": null,
      "taskData": null,
      "promptReference": null
    }
  ]
}
```

**响应字段说明**:
- `chatId`: 聊天记录唯一ID
- `dateTime`: 消息时间
- `text`: 消息内容
- `modelType`: 模型类型 (1-文本, 2-图像, 3-音频)
- `status`: 消息状态 (1-正常, 2-处理中, 3-失败)
- `inversion`: 是否为用户消息 (true-用户, false-AI)
- `error`: 是否有错误
- `model`: 使用的模型名称
- `modelName`: 模型显示名称
- `modelAvatar`: 模型头像
- `fileInfo`: 文件信息 (JSON字符串)
- `ttsUrl`: 语音文件URL
- `videoUrl`: 视频文件URL
- `audioUrl`: 音频文件URL
- `pluginParam`: 插件参数
- `taskData`: 任务数据
- `promptReference`: 提示词引用

**前端实现示例**:
```typescript
async function fetchChatList(groupId?: number) {
  try {
    const response = await get<any>({
      url: '/chatlog/chatList',
      data: { groupId }
    });
    return response.data;
  } catch (error) {
    console.error('获取聊天记录失败:', error);
    throw error;
  }
}
```

### 2. 删除单条聊天记录

**接口地址**: `POST /chatlog/del`

**功能描述**: 删除指定的单条聊天记录。

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface DeleteChatRequest {
  id: number;                        // 聊天记录ID
}
```

**请求示例**:
```json
{
  "id": 12345
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "删除对话记录成功！"
}
```

**前端实现示例**:
```typescript
async function deleteChatLog(id: number) {
  try {
    const response = await post<any>({
      url: '/chatlog/del',
      data: { id }
    });
    return response.message;
  } catch (error) {
    console.error('删除聊天记录失败:', error);
    throw error;
  }
}
```

### 3. 删除对话组所有记录

**接口地址**: `POST /chatlog/delByGroupId`

**功能描述**: 清空指定对话组的所有聊天记录。

**请求参数**:
```typescript
interface DeleteGroupChatsRequest {
  groupId: number;                   // 对话组ID
}
```

**请求示例**:
```json
{
  "groupId": 123
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "删除对话记录成功！"
}
```

**前端实现示例**:
```typescript
async function deleteGroupChats(groupId: number) {
  try {
    const response = await post<any>({
      url: '/chatlog/delByGroupId',
      data: { groupId }
    });
    return response.message;
  } catch (error) {
    console.error('删除对话组记录失败:', error);
    throw error;
  }
}
```

### 4. 删除指定消息后的所有记录

**接口地址**: `POST /chatlog/deleteChatsAfterId`

**功能描述**: 删除对话组中某条消息及其后的所有聊天记录。

**请求参数**:
```typescript
interface DeleteChatsAfterIdRequest {
  id: number;                        // 聊天记录ID，删除此ID及之后的所有记录
}
```

**请求示例**:
```json
{
  "id": 12345
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "删除对话记录成功！"
}
```

**前端实现示例**:
```typescript
async function deleteChatsAfterId(id: number) {
  try {
    const response = await post<any>({
      url: '/chatlog/deleteChatsAfterId',
      data: { id }
    });
    return response.message;
  } catch (error) {
    console.error('删除后续聊天记录失败:', error);
    throw error;
  }
}
```

### 5. 查询单个应用的对话记录

**接口地址**: `GET /chatlog/byAppId`

**功能描述**: 查询指定应用的聊天记录，支持分页。

**请求参数**:
```typescript
interface QueryByAppIdRequest {
  appId: number;                     // 应用ID
  page?: number;                     // 页码，默认1
  size?: number;                     // 每页数量，默认20
}
```

**请求示例**:
```
GET /chatlog/byAppId?appId=10&page=1&size=20
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "chatId": 12345,
        "dateTime": "2024-01-20 10:30:00",
        "text": "消息内容",
        "modelType": 1,
        "status": 1,
        "inversion": true,
        "error": false
      }
    ],
    "count": 100,
    "page": 1,
    "size": 20
  }
}
```

**前端实现示例**:
```typescript
async function fetchChatLogByAppId(appId: number, page = 1, size = 20) {
  try {
    const response = await get<any>({
      url: '/chatlog/byAppId',
      data: { appId, page, size }
    });
    return response.data;
  } catch (error) {
    console.error('获取应用聊天记录失败:', error);
    throw error;
  }
}
```

## 错误处理

### 常见错误情况

1. **记录不存在** (400)
```json
{
  "success": false,
  "message": "你删除的对话记录不存在、请检查！",
  "code": "RECORD_NOT_FOUND"
}
```

2. **权限不足** (403)
```json
{
  "success": false,
  "message": "无权限操作此记录",
  "code": "PERMISSION_DENIED"
}
```

3. **没有可删除的记录** (400)
```json
{
  "success": false,
  "message": "当前页面已经没有东西可以删除了！",
  "code": "NO_RECORDS_TO_DELETE"
}
```

### 错误处理示例

```typescript
async function handleChatLogError(error: any, operation: string) {
  const status = error.response?.status;
  const message = error.response?.data?.message || error.message;

  switch (status) {
    case 400:
      if (message.includes('不存在')) {
        showErrorMessage('记录不存在或已被删除');
      } else if (message.includes('没有东西可以删除')) {
        showInfoMessage('没有可删除的记录');
      } else {
        showErrorMessage(message);
      }
      break;
    case 403:
      showErrorMessage('无权限执行此操作');
      break;
    default:
      showErrorMessage(`${operation}失败，请稍后重试`);
  }
}
```

## 使用建议

### 1. 数据缓存策略

```typescript
// 使用 Pinia store 缓存聊天记录
const useChatLogStore = defineStore('chatLog', {
  state: () => ({
    chatLogs: new Map<number, any[]>(), // groupId -> chatLogs
    lastUpdateTime: new Map<number, number>()
  }),

  actions: {
    async getChatLogs(groupId: number, forceRefresh = false) {
      const now = Date.now();
      const lastUpdate = this.lastUpdateTime.get(groupId) || 0;
      
      // 5分钟内不重复请求
      if (!forceRefresh && now - lastUpdate < 5 * 60 * 1000) {
        return this.chatLogs.get(groupId) || [];
      }

      try {
        const logs = await fetchChatList(groupId);
        this.chatLogs.set(groupId, logs);
        this.lastUpdateTime.set(groupId, now);
        return logs;
      } catch (error) {
        console.error('获取聊天记录失败:', error);
        return this.chatLogs.get(groupId) || [];
      }
    }
  }
});
```

### 2. 批量操作确认

```typescript
async function confirmBatchDelete(operation: string, count?: number) {
  const message = count 
    ? `确定要删除 ${count} 条聊天记录吗？`
    : `确定要${operation}吗？`;
    
  return new Promise((resolve) => {
    showConfirmDialog({
      title: '确认操作',
      message,
      onConfirm: () => resolve(true),
      onCancel: () => resolve(false)
    });
  });
}
```

### 3. 实时更新

```typescript
// 监听聊天记录变化
watch(() => chatStore.active, async (newGroupId) => {
  if (newGroupId) {
    await chatLogStore.getChatLogs(newGroupId);
  }
});

// 新消息后更新记录
async function afterSendMessage(groupId: number) {
  await chatLogStore.getChatLogs(groupId, true); // 强制刷新
}
```
