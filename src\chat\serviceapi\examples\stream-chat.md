# 流式聊天功能示例

## 概述

本文档详细介绍了 DeepCreate 项目中流式聊天功能的实现方法，包括流式数据处理、实时UI更新、错误处理等关键技术点。

## 1. 基础流式聊天实现

### 1.1 流式响应处理器

```typescript
interface StreamHandler {
  onProgress: (data: any) => void;
  onComplete: () => void;
  onError: (error: Error) => void;
}

class StreamChatProcessor {
  private abortController: AbortController | null = null;
  private buffer = '';

  async processStreamChat(
    request: ChatProcessRequest,
    handler: StreamHandler
  ) {
    this.abortController = new AbortController();

    try {
      const response = await fetch('/chatgpt/chat-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify(request),
        signal: this.abortController.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      await this.readStream(response, handler);
    } catch (error) {
      if (error.name !== 'AbortError') {
        handler.onError(error);
      }
    }
  }

  private async readStream(response: Response, handler: StreamHandler) {
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error('无法获取响应流');
    }

    try {
      while (true) {
        const { value, done } = await reader.read();
        
        if (done) {
          handler.onComplete();
          break;
        }

        const text = decoder.decode(value, { stream: true });
        this.buffer += text;

        // 处理缓冲区中的完整行
        this.processBuffer(handler);
      }
    } finally {
      reader.releaseLock();
    }
  }

  private processBuffer(handler: StreamHandler) {
    const lines = this.buffer.split('\n');
    this.buffer = lines.pop() || ''; // 保留最后一个不完整的行

    for (const line of lines) {
      if (line.trim()) {
        try {
          const data = JSON.parse(line);
          handler.onProgress(data);
        } catch (error) {
          console.warn('解析流式数据失败:', error, line);
        }
      }
    }
  }

  abort() {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  isActive() {
    return this.abortController !== null;
  }
}
```

### 1.2 流式聊天组件实现

```typescript
import { ref, onUnmounted } from 'vue';
import { useChatStore } from '@/store/modules/chat';

export function useStreamChat() {
  const chatStore = useChatStore();
  const streamProcessor = new StreamChatProcessor();
  const isStreaming = ref(false);
  const currentStreamText = ref('');

  // 发送流式消息
  async function sendStreamMessage(
    message: string,
    options: Partial<ChatProcessRequest> = {}
  ) {
    const activeGroupId = chatStore.active;
    if (!activeGroupId) {
      throw new Error('请先选择对话组');
    }

    // 添加用户消息
    chatStore.addGroupChat({
      text: message,
      inversion: true,
      error: false,
      loading: false,
      modelType: 1
    });

    // 添加AI回复占位符
    const aiMessageIndex = chatStore.chatList.length;
    chatStore.addGroupChat({
      text: '',
      inversion: false,
      error: false,
      loading: true,
      modelType: 1,
      model: options.model || 'gpt-3.5-turbo',
      modelName: options.modelName || 'GPT-3.5'
    });

    isStreaming.value = true;
    currentStreamText.value = '';

    const request: ChatProcessRequest = {
      prompt: message,
      model: 'gpt-3.5-turbo',
      modelName: 'GPT-3.5',
      modelType: 1,
      options: {
        groupId: activeGroupId,
        usingNetwork: false
      },
      ...options
    };

    try {
      await streamProcessor.processStreamChat(request, {
        onProgress: (data) => handleStreamProgress(data, aiMessageIndex),
        onComplete: () => handleStreamComplete(aiMessageIndex),
        onError: (error) => handleStreamError(error, aiMessageIndex)
      });
    } catch (error) {
      handleStreamError(error, aiMessageIndex);
    }
  }

  // 处理流式数据
  function handleStreamProgress(data: any, messageIndex: number) {
    if (data.text) {
      currentStreamText.value += data.text;
      
      // 更新UI中的消息
      chatStore.updateGroupChat(messageIndex, {
        text: currentStreamText.value,
        loading: true,
        error: false
      });
    }

    // 更新用户余额
    if (data.userBalance !== undefined) {
      const authStore = useAuthStore();
      authStore.updateUserBalance(data.userBalance);
    }

    // 更新聊天ID
    if (data.chatId) {
      chatStore.updateGroupChat(messageIndex, {
        chatId: data.chatId
      });
    }

    // 处理文件信息
    if (data.fileInfo) {
      chatStore.updateGroupChat(messageIndex, {
        fileInfo: data.fileInfo
      });
    }
  }

  // 流式完成处理
  function handleStreamComplete(messageIndex: number) {
    isStreaming.value = false;
    
    chatStore.updateGroupChat(messageIndex, {
      loading: false,
      error: false
    });

    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }

  // 流式错误处理
  function handleStreamError(error: any, messageIndex: number) {
    isStreaming.value = false;
    currentStreamText.value = '';

    console.error('流式聊天错误:', error);

    chatStore.updateGroupChat(messageIndex, {
      text: '抱歉，回复失败了，请重试',
      loading: false,
      error: true
    });

    // 显示错误提示
    showErrorMessage('消息发送失败，请重试');
  }

  // 停止流式输出
  function stopStream() {
    if (streamProcessor.isActive()) {
      streamProcessor.abort();
      isStreaming.value = false;
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopStream();
  });

  return {
    sendStreamMessage,
    stopStream,
    isStreaming: readonly(isStreaming),
    currentStreamText: readonly(currentStreamText)
  };
}
```

## 2. 高级流式功能

### 2.1 流式数据缓存和恢复

```typescript
interface StreamCache {
  messageId: string;
  content: string;
  timestamp: number;
  completed: boolean;
}

class StreamCacheManager {
  private cache = new Map<string, StreamCache>();
  private readonly CACHE_KEY = 'stream_chat_cache';
  private readonly MAX_CACHE_AGE = 30 * 60 * 1000; // 30分钟

  // 保存流式数据到缓存
  saveStreamData(messageId: string, content: string, completed = false) {
    const cacheData: StreamCache = {
      messageId,
      content,
      timestamp: Date.now(),
      completed
    };

    this.cache.set(messageId, cacheData);
    this.persistCache();
  }

  // 获取缓存的流式数据
  getStreamData(messageId: string): StreamCache | null {
    const data = this.cache.get(messageId);
    
    if (data && this.isValidCache(data)) {
      return data;
    }

    return null;
  }

  // 恢复未完成的流式对话
  getIncompleteStreams(): StreamCache[] {
    this.loadCache();
    
    return Array.from(this.cache.values())
      .filter(data => !data.completed && this.isValidCache(data));
  }

  // 清理过期缓存
  cleanExpiredCache() {
    const now = Date.now();
    
    for (const [key, data] of this.cache.entries()) {
      if (now - data.timestamp > this.MAX_CACHE_AGE) {
        this.cache.delete(key);
      }
    }
    
    this.persistCache();
  }

  private isValidCache(data: StreamCache): boolean {
    return Date.now() - data.timestamp < this.MAX_CACHE_AGE;
  }

  private persistCache() {
    try {
      const cacheArray = Array.from(this.cache.entries());
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheArray));
    } catch (error) {
      console.warn('保存流式缓存失败:', error);
    }
  }

  private loadCache() {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const cacheArray = JSON.parse(cached);
        this.cache = new Map(cacheArray);
      }
    } catch (error) {
      console.warn('加载流式缓存失败:', error);
      this.cache.clear();
    }
  }
}

// 使用缓存管理器
const streamCacheManager = new StreamCacheManager();

// 在流式处理中使用缓存
function handleStreamProgressWithCache(data: any, messageId: string, messageIndex: number) {
  if (data.text) {
    const existingCache = streamCacheManager.getStreamData(messageId);
    const newContent = (existingCache?.content || '') + data.text;
    
    // 保存到缓存
    streamCacheManager.saveStreamData(messageId, newContent, false);
    
    // 更新UI
    chatStore.updateGroupChat(messageIndex, {
      text: newContent,
      loading: true,
      error: false
    });
  }
}
```

### 2.2 流式输出优化

```typescript
class StreamOptimizer {
  private updateQueue: Array<{ index: number; data: any }> = [];
  private isProcessing = false;
  private batchSize = 5;
  private updateInterval = 50; // 50ms

  // 批量更新UI
  queueUpdate(messageIndex: number, data: any) {
    this.updateQueue.push({ index: messageIndex, data });
    
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  private async processQueue() {
    this.isProcessing = true;

    while (this.updateQueue.length > 0) {
      const batch = this.updateQueue.splice(0, this.batchSize);
      
      // 合并相同索引的更新
      const mergedUpdates = this.mergeUpdates(batch);
      
      // 批量更新
      for (const update of mergedUpdates) {
        chatStore.updateGroupChat(update.index, update.data);
      }

      // 等待下一批处理
      if (this.updateQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.updateInterval));
      }
    }

    this.isProcessing = false;
  }

  private mergeUpdates(batch: Array<{ index: number; data: any }>) {
    const merged = new Map<number, any>();

    for (const update of batch) {
      const existing = merged.get(update.index) || {};
      merged.set(update.index, { ...existing, ...update.data });
    }

    return Array.from(merged.entries()).map(([index, data]) => ({ index, data }));
  }
}

const streamOptimizer = new StreamOptimizer();

// 优化后的流式处理
function handleOptimizedStreamProgress(data: any, messageIndex: number) {
  if (data.text) {
    streamOptimizer.queueUpdate(messageIndex, {
      text: (chatStore.chatList[messageIndex]?.text || '') + data.text,
      loading: true
    });
  }
}
```

## 3. 流式聊天UI组件

### 3.1 打字机效果组件

```vue
<template>
  <div class="stream-text">
    <span 
      v-for="(char, index) in displayedChars" 
      :key="index"
      :class="{ 'typing-char': index === displayedChars.length - 1 }"
    >
      {{ char }}
    </span>
    <span v-if="isTyping" class="cursor">|</span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';

interface Props {
  text: string;
  speed?: number;
  autoStart?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  speed: 50,
  autoStart: true
});

const displayedChars = ref<string[]>([]);
const isTyping = ref(false);
const currentIndex = ref(0);
let typingTimer: number | null = null;

// 开始打字效果
function startTyping() {
  if (isTyping.value) return;
  
  isTyping.value = true;
  currentIndex.value = 0;
  displayedChars.value = [];
  
  typeNextChar();
}

// 打字下一个字符
function typeNextChar() {
  if (currentIndex.value < props.text.length) {
    displayedChars.value.push(props.text[currentIndex.value]);
    currentIndex.value++;
    
    typingTimer = setTimeout(typeNextChar, props.speed);
  } else {
    isTyping.value = false;
  }
}

// 停止打字效果
function stopTyping() {
  if (typingTimer) {
    clearTimeout(typingTimer);
    typingTimer = null;
  }
  isTyping.value = false;
  
  // 显示完整文本
  displayedChars.value = props.text.split('');
  currentIndex.value = props.text.length;
}

// 监听文本变化
watch(() => props.text, (newText) => {
  if (newText && props.autoStart) {
    startTyping();
  }
});

onMounted(() => {
  if (props.text && props.autoStart) {
    startTyping();
  }
});

onUnmounted(() => {
  stopTyping();
});

defineExpose({
  startTyping,
  stopTyping,
  isTyping
});
</script>

<style scoped>
.stream-text {
  font-family: inherit;
  line-height: 1.6;
}

.typing-char {
  animation: fadeIn 0.1s ease-in;
}

.cursor {
  animation: blink 1s infinite;
  color: #007bff;
  font-weight: bold;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
```

### 3.2 流式消息组件

```vue
<template>
  <div class="stream-message" :class="{ 'user-message': message.inversion }">
    <div class="message-avatar">
      <img 
        :src="message.modelAvatar || defaultAvatar" 
        :alt="message.modelName"
        class="avatar-img"
      />
    </div>
    
    <div class="message-content">
      <div class="message-header">
        <span class="model-name">{{ message.modelName }}</span>
        <span class="message-time">{{ formatTime(message.dateTime) }}</span>
      </div>
      
      <div class="message-body">
        <StreamText
          v-if="!message.inversion && isStreaming"
          :text="message.text"
          :speed="30"
          @complete="handleTypingComplete"
        />
        <div v-else class="message-text" v-html="formatMessage(message.text)"></div>
        
        <!-- 加载状态 -->
        <div v-if="message.loading" class="loading-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        
        <!-- 错误状态 -->
        <div v-if="message.error" class="error-indicator">
          <Icon name="error" />
          <span>发送失败</span>
          <button @click="$emit('retry')" class="retry-btn">重试</button>
        </div>
        
        <!-- 文件信息 -->
        <FileDisplay 
          v-if="message.fileInfo" 
          :file-info="message.fileInfo"
        />
      </div>
      
      <!-- 消息操作 -->
      <div class="message-actions">
        <button 
          v-if="!message.inversion && !message.loading"
          @click="copyMessage"
          class="action-btn"
          title="复制"
        >
          <Icon name="copy" />
        </button>
        
        <button 
          v-if="message.ttsUrl"
          @click="playAudio"
          class="action-btn"
          title="播放语音"
        >
          <Icon name="play" />
        </button>
        
        <button 
          v-if="!message.inversion"
          @click="$emit('regenerate')"
          class="action-btn"
          title="重新生成"
        >
          <Icon name="refresh" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import StreamText from './StreamText.vue';
import FileDisplay from './FileDisplay.vue';
import Icon from '@/components/common/Icon.vue';

interface Props {
  message: ChatMessage;
  isStreaming?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['retry', 'regenerate', 'typingComplete']);

const defaultAvatar = '/src/assets/avatar.png';

// 格式化消息内容
function formatMessage(text: string) {
  // 处理 Markdown、代码块等
  return text
    .replace(/\n/g, '<br>')
    .replace(/`([^`]+)`/g, '<code>$1</code>');
}

// 格式化时间
function formatTime(dateTime?: string) {
  if (!dateTime) return '';
  
  const date = new Date(dateTime);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 复制消息
async function copyMessage() {
  try {
    await navigator.clipboard.writeText(props.message.text);
    showSuccessMessage('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    showErrorMessage('复制失败');
  }
}

// 播放音频
function playAudio() {
  if (props.message.ttsUrl) {
    const audio = new Audio(props.message.ttsUrl);
    audio.play().catch(error => {
      console.error('播放音频失败:', error);
      showErrorMessage('播放失败');
    });
  }
}

// 打字完成处理
function handleTypingComplete() {
  emit('typingComplete');
}
</script>

<style scoped>
.stream-message {
  display: flex;
  gap: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.avatar-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.model-name {
  font-weight: 600;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-body {
  background: #f5f5f5;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
}

.user-message .message-body {
  background: #007bff;
  color: white;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.error-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff4757;
  margin-top: 8px;
}

.retry-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.stream-message:hover .message-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f0f0f0;
  color: #333;
}
</style>
```
