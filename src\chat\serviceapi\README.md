# DeepCreate 聊天功能 API 对接文档

## 概述

本文档详细描述了 DeepCreate 项目前端聊天页面与后端 API 的对接规范，包括所有聊天相关接口的使用方法、参数说明、响应格式和错误处理。

## 目录结构

```
serviceapi/
├── README.md                    # 本文档
├── chat-api.md                  # 聊天核心接口文档
├── chatlog-api.md              # 聊天记录管理接口文档
├── group-api.md                # 对话组管理接口文档
├── models-api.md               # 模型配置接口文档
├── types.md                    # TypeScript 类型定义
├── examples/                   # 使用示例
│   ├── basic-chat.md           # 基础聊天示例
│   ├── stream-chat.md          # 流式聊天示例
│   ├── file-upload.md          # 文件上传聊天示例
│   └── error-handling.md       # 错误处理示例
└── utils/                      # 工具函数
    ├── api-client.md           # API 客户端配置
    ├── stream-parser.md        # 流式响应解析
    └── error-handler.md        # 错误处理工具
```

## 快速开始

### 1. 基础配置

```typescript
// 配置 API 基础地址
const API_BASE_URL = 'http://localhost:9520';

// 配置请求头
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`
};
```

### 2. 基础聊天示例

```typescript
import { fetchChatAPIProcess } from '@/api';

// 发送聊天消息
const sendMessage = async (message: string, groupId: number) => {
  try {
    const response = await fetchChatAPIProcess({
      model: 'gpt-3.5-turbo',
      modelName: 'GPT-3.5',
      modelType: 1,
      prompt: message,
      options: {
        groupId,
        usingNetwork: false
      }
    });
    
    return response;
  } catch (error) {
    console.error('发送消息失败:', error);
    throw error;
  }
};
```

### 3. 流式聊天示例

```typescript
// 流式聊天处理
const handleStreamChat = async (message: string, onProgress: (data: any) => void) => {
  const response = await fetch('/chatgpt/chat-process', {
    method: 'POST',
    headers,
    body: JSON.stringify({
      prompt: message,
      options: { groupId: 1 }
    })
  });

  const reader = response.body?.getReader();
  const decoder = new TextDecoder();

  while (true) {
    const { value, done } = await reader.read();
    if (done) break;

    const text = decoder.decode(value);
    const lines = text.split('\n');

    for (const line of lines) {
      if (line.trim()) {
        try {
          const data = JSON.parse(line);
          onProgress(data);
        } catch (e) {
          console.error('解析流式数据失败:', e);
        }
      }
    }
  }
};
```

## 核心接口概览

### 聊天接口
- `POST /chatgpt/chat-process` - 流式聊天对话
- `POST /chatgpt/chat-sync` - 同步聊天对话
- `POST /chatgpt/mj-fy` - MJ 描述词翻译
- `POST /chatgpt/chat-mind` - 思维导图生成
- `POST /chatgpt/tts-process` - 文字转语音

### 聊天记录接口
- `GET /chatlog/chatList` - 查询聊天记录列表
- `POST /chatlog/del` - 删除单条聊天记录
- `POST /chatlog/delByGroupId` - 删除对话组所有记录
- `POST /chatlog/deleteChatsAfterId` - 删除指定消息后的所有记录

### 对话组接口
- `POST /group/create` - 创建对话组
- `GET /group/query` - 查询对话组列表
- `GET /group/info/:id` - 获取对话组详情
- `POST /group/update` - 更新对话组
- `POST /group/del` - 删除对话组

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "data": null
}
```

### 流式响应
流式接口返回的是 JSON 对象流，每行一个 JSON 对象：
```
{"text": "你好"}
{"text": "，我是"}
{"text": "AI助手"}
{"userBalance": 100}
```

## 错误处理

### 常见错误码
- `401` - 未授权，需要登录
- `403` - 权限不足
- `400` - 请求参数错误
- `500` - 服务器内部错误
- `429` - 请求频率过高

### 错误处理示例
```typescript
try {
  const response = await apiCall();
  return response.data;
} catch (error) {
  if (error.response?.status === 401) {
    // 跳转到登录页
    router.push('/login');
  } else if (error.response?.status === 429) {
    // 显示频率限制提示
    showMessage('请求过于频繁，请稍后再试');
  } else {
    // 显示通用错误信息
    showMessage(error.message || '操作失败');
  }
  throw error;
}
```

## 注意事项

1. **认证**: 所有接口都需要在请求头中携带有效的 JWT Token
2. **流式响应**: 流式接口需要特殊处理，不能使用普通的 axios 请求
3. **错误重试**: 建议实现自动重试机制，特别是网络错误的情况
4. **数据缓存**: 对话组列表等数据建议进行本地缓存
5. **内存管理**: 长时间的流式对话需要注意内存泄漏问题

## 更新日志

- **v1.0.0** (2024-01-20): 初始版本，包含基础聊天功能
- **v1.1.0** (2024-01-25): 新增流式聊天支持
- **v1.2.0** (2024-01-30): 新增文件上传功能
- **v1.3.0** (2024-02-05): 新增思维导图和 TTS 功能

## 联系方式

如有问题或建议，请联系开发团队。
